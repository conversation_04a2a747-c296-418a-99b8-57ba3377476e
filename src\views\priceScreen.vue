<!-- 物价屏 -->
<template>
  <div class="page-container">
    <div class="table-section">
      <div class="table-header">
        <h2>商品价格信息</h2>
      </div>
      <div class="table-wrapper" v-loading="loading">
        <div class="scrolling-table" ref="scrollingTable">
          <el-table :data="priceData" stripe :show-header="true" height="100%" class="price-table">
            <el-table-column prop="ItemCode" label="编码" min-width="120" align="center" />
            <el-table-column prop="name" label="名称" min-width="200" align="center" />
            <el-table-column prop="SpecInfo" label="规格" min-width="150" align="center" />
            <el-table-column prop="Uom" label="单位" min-width="80" align="center" />
            <el-table-column prop="TarSubCate" label="类型" min-width="120" align="center" />
            <el-table-column prop="Price" label="单价(元)" min-width="120" align="center">
              <template slot-scope="scope">
                <span class="price-text">{{ priceText(scope.row.Price) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 加载更多提示 -->
        <div v-if="isLoadingMore" class="loading-more-tip">
          <i class="el-icon-loading"></i>
          <span>正在加载更多数据...</span>
        </div>

        <!-- 数据统计信息 -->
        <div v-if="!loading && totalCount > 0" class="data-info">
          <span>已显示 {{ priceData.length }} / {{ totalCount }} 条记录</span>
          <span v-if="!hasMoreData" class="no-more-data">（已加载全部数据）</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toFixed } from "@/utils/money";
import priceTestData from "./priceTestData.js";

export default {
  name: "priceScreen",
  data() {
    return {
      loading: true,
      videoError: false,
      videoSrc: require("@/assets/videos/price_screen_video.mp4"),
      scrollTimer: null,
      scrollSpeed: 0.5, // 滚动速度（像素/帧）
      currentScrollTop: 0, // 当前滚动位置

      // 分页相关
      currentPage: 1, // 当前页码
      pageSize: 50, // 每页条数
      totalPages: 0, // 总页数
      totalCount: 0, // 总记录数
      hasMoreData: true, // 是否还有更多数据
      isLoadingMore: false, // 是否正在加载更多数据

      // 无限滚动相关
      originalData: [], // 原始数据
      priceData: [], // 显示的数据
      appendCount: 0, // 追加次数
      maxAppendCount: 5, // 最大追加次数，超过后清理
      minDataForScroll: 10, // 最少需要多少条数据才开始滚动

      // 滚动检测相关
      scrollCheckDistance: 200, // 距离底部多少像素时触发加载
      scrollCheckTimer: null, // 滚动检测定时器
    };
  },

  computed: {
    priceText() {
      return value => {
        const price = toFixed(value, 4);
        return price ? price + "元" : "--";
      };
    },
  },
  created() {
    // 初始化数据
    this.originalData = [...priceTestData];
    this.priceData = [...priceTestData];
    this.loadInitialData();
  },

  mounted() {
    this.initPage();
  },

  methods: {
    // 加载初始数据（第一页）
    async loadInitialData() {
      this.loading = true;
      this.currentPage = 1;
      this.priceData = [];

      try {
        const res = await this.$api.price.queryPrice({
          alias: "", // 空值查询全部
          page: this.currentPage,
          pageSize: this.pageSize
        });

        console.log("物价接口初始数据", res);

        if (res.success && res.data.ResultCode === "0") {
          // 处理返回的数据
          this.handleApiResponse(res, true);
        } else {
          this.$message.error(res.message || "数据加载失败");
          // 如果API失败，使用测试数据
          this.useTestData();
        }
      } catch (error) {
        console.error("加载初始数据失败:", error);
        this.$message.error("数据加载异常");
        // 如果API异常，使用测试数据
        this.useTestData();
      } finally {
        this.loading = false;
      }
    },

    // 加载更多数据（下一页）
    async loadMoreData() {
      if (this.isLoadingMore || !this.hasMoreData) {
        return;
      }

      this.isLoadingMore = true;
      this.currentPage++;

      try {
        const res = await this.$api.price.queryPrice({
          alias: "", // 空值查询全部
          page: this.currentPage,
          pageSize: this.pageSize
        });

        console.log(`物价接口第${this.currentPage}页数据`, res);

        if (res.success && res.data.ResultCode === "0") {
          // 处理返回的数据
          this.handleApiResponse(res, false);
        } else {
          this.$message.error(res.message || "加载更多数据失败");
          this.currentPage--; // 回退页码
        }
      } catch (error) {
        console.error("加载更多数据失败:", error);
        this.$message.error("加载更多数据异常");
        this.currentPage--; // 回退页码
      } finally {
        this.isLoadingMore = false;
      }
    },

    // 处理API响应数据
    handleApiResponse(res, isInitial = false) {
      const { data, pagination } = res;

      // 更新分页信息
      this.totalPages = pagination.totalPages;
      this.totalCount = pagination.totalCount;
      this.hasMoreData = this.currentPage < this.totalPages;

      // 处理数据项
      let newItems = [];
      if (data.TarItemS && data.TarItemS.TarItem) {
        const items = Array.isArray(data.TarItemS.TarItem)
          ? data.TarItemS.TarItem
          : [data.TarItemS.TarItem];

        newItems = items.map(item => ({
          ItemCode: item.ItemCode || "",
          name: item.ItemDesc || "",
          SpecInfo: item.SpecInfo || "",
          Uom: item.Uom || "",
          TarSubCate: item.TarSubCate || "",
          Price: item.Price || ""
        }));
      }

      if (isInitial) {
        // 初始加载，替换数据
        this.priceData = newItems;
        this.originalData = [...newItems];
      } else {
        // 追加数据
        this.priceData = [...this.priceData, ...newItems];
      }

      console.log(`数据处理完成，当前共${this.priceData.length}条记录，总共${this.totalCount}条，还有${this.hasMoreData ? '更多' : '没有更多'}数据`);
    },

    // 使用测试数据（API失败时的备用方案）
    useTestData() {
      this.priceData = [...priceTestData];
      this.originalData = [...priceTestData];
      this.totalCount = priceTestData.length;
      this.totalPages = 1;
      this.hasMoreData = false;
      console.log("使用测试数据，共", priceTestData.length, "条记录");
    },

    // 初始化页面
    async initPage() {
      this.$nextTick(() => {
        // 延迟一点时间确保表格完全渲染
        setTimeout(() => {
          this.setupScrollDetection();
          this.checkAndStartScrolling();
        }, 500);
      });
    },

    // 设置滚动检测
    setupScrollDetection() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
      if (!tableBody) {
        console.log("表格主体包装器tableBody未找到");
        return;
      }

      // 添加滚动事件监听
      tableBody.addEventListener('scroll', this.handleScroll);
      console.log("滚动检测已设置");
    },

    // 处理滚动事件
    handleScroll(event) {
      // 使用防抖，避免频繁触发
      if (this.scrollCheckTimer) {
        clearTimeout(this.scrollCheckTimer);
      }

      this.scrollCheckTimer = setTimeout(() => {
        this.checkScrollPosition(event.target);
      }, 100);
    },

    // 检查滚动位置，判断是否需要加载更多数据
    checkScrollPosition(scrollElement) {
      if (!scrollElement || this.isLoadingMore || !this.hasMoreData) {
        return;
      }

      const scrollTop = scrollElement.scrollTop;
      const scrollHeight = scrollElement.scrollHeight;
      const clientHeight = scrollElement.clientHeight;

      // 计算距离底部的距离
      const distanceToBottom = scrollHeight - scrollTop - clientHeight;

      // 如果距离底部小于设定值，触发加载更多
      if (distanceToBottom <= this.scrollCheckDistance) {
        console.log(`距离底部${distanceToBottom}px，触发加载更多数据`);
        this.loadMoreData();
      }
    },

    // 检查是否需要滚动并开始滚动
    checkAndStartScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      this.$nextTick(() => {
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (!tableBody) {
          console.log("表格主体包装器tableBody未找到");
          return;
        }

        // 检查是否需要滚动（内容高度大于容器高度）
        const scrollHeight = tableBody.scrollHeight;
        const clientHeight = tableBody.clientHeight;

        console.log(`scrollHeight: ${scrollHeight}, clientHeight: ${clientHeight}`);

        if (scrollHeight > clientHeight) {
          console.log("内容需要滚动,开始滚动动画");
          this.startScrolling();
        } else {
          console.log("内容完全适配容器,无需滚动操作");
          // 如果数据不够滚动且还有更多数据，尝试加载更多
          if (this.priceData.length < this.minDataForScroll && this.hasMoreData) {
            console.log("数据量太少且还有更多数据，尝试加载更多");
            this.loadMoreData().then(() => {
              // 加载完成后重新检查
              setTimeout(() => {
                this.checkAndStartScrolling();
              }, 500);
            });
          } else if (this.priceData.length < this.minDataForScroll) {
            console.log("数据量太少且没有更多数据，复制数据以支持滚动");
            this.duplicateDataForScrolling();
          }
        }
      });
    },

    // 复制数据以确保有足够的内容进行滚动
    duplicateDataForScrolling() {
      console.log("复制数据以支持滚动");

      // 如果没有更多数据且当前数据不足，复制现有数据多次
      if (!this.hasMoreData && this.priceData.length > 0) {
        const currentData = [...this.priceData];
        const targetLength = Math.max(this.minDataForScroll, currentData.length * 3);
        const newData = [];

        while (newData.length < targetLength) {
          newData.push(...currentData);
        }

        this.priceData = newData.slice(0, targetLength);
        this.appendCount = Math.floor(targetLength / currentData.length) - 1;

        console.log(`数据已复制，总共${this.priceData.length}条记录`);

        this.$nextTick(() => {
          this.startScrolling();
        });
      }
    },

    // 开始滚动动画
    startScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("滚动表格的Ref未找到");
        return;
      }

      // 等待DOM渲染完成后再开始滚动
      this.$nextTick(() => {
        // 查找表格的body容器
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (!tableBody) {
          console.log("表格主体包装器tableBody未找到");
          return;
        }

        console.log("开始滚动动画");
        const scroll = () => {
          this.currentScrollTop += this.scrollSpeed;

          // 获取表格的实际高度
          const scrollHeight = tableBody.scrollHeight;
          const clientHeight = tableBody.clientHeight;
          const maxScroll = scrollHeight - clientHeight;

          // 当滚动接近底部时，追加数据
          if (this.currentScrollTop >= maxScroll - 100) {
            // 提前100px开始追加
            this.appendDataForInfiniteScroll();
            return; // 追加数据后会重新开始滚动
          }

          tableBody.scrollTop = this.currentScrollTop;
          this.scrollTimer = requestAnimationFrame(scroll);
        };

        this.scrollTimer = requestAnimationFrame(scroll);
      });
    },

    // 为无限滚动追加数据
    appendDataForInfiniteScroll() {
      console.log(`附加数据,当前附加次数: ${this.appendCount}`);

      // 检查是否需要清理数据
      if (this.appendCount >= this.maxAppendCount) {
        console.log("达到最大追加次数，正在清理数据");
        this.cleanupAndResetData();
        return;
      }

      // 在数组头部追加原始数据
      this.priceData = [...this.originalData, ...this.priceData];
      this.appendCount++;

      // 重新计算滚动位置，保持视觉连续性
      this.$nextTick(() => {
        const tableWrapper = this.$refs.scrollingTable;
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");

        if (tableBody) {
          // 计算原始数据的高度
          const rowHeight = tableBody.scrollHeight / this.priceData.length;
          const originalDataHeight = rowHeight * this.originalData.length;

          // 调整滚动位置
          this.currentScrollTop += originalDataHeight;
          tableBody.scrollTop = this.currentScrollTop;

          console.log(`数据已添加,新的滚动位置: ${this.currentScrollTop}`);

          // 继续滚动
          this.startScrolling();
        }
      });
    },

    // 清理数据并重置
    cleanupAndResetData() {
      console.log("清理数据并重置");

      // 重置为原始数据的3倍（保证有足够内容滚动）
      this.priceData = [...this.originalData, ...this.originalData, ...this.originalData];
      this.appendCount = 2; // 已经有3倍数据，所以追加次数为2
      this.currentScrollTop = 0;

      this.$nextTick(() => {
        this.startScrolling();
      });
    },

    // 清除滚动定时器
    clearScrollTimer() {
      if (this.scrollTimer) {
        cancelAnimationFrame(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 视频错误处理
    handleVideoError() {
      this.videoError = true;
      console.error("视频加载失败");
    },

    // 视频开始加载
    handleVideoLoadStart() {
      this.videoError = false;
    },

    // 视频可以播放
    handleVideoCanPlay() {
      this.videoError = false;
    },
  },
  beforeDestroy() {
    this.clearScrollTimer();

    // 清理滚动检测定时器
    if (this.scrollCheckTimer) {
      clearTimeout(this.scrollCheckTimer);
      this.scrollCheckTimer = null;
    }

    // 移除滚动事件监听
    const tableWrapper = this.$refs.scrollingTable;
    if (tableWrapper) {
      const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
      if (tableBody) {
        tableBody.removeEventListener('scroll', this.handleScroll);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
$leftWidth: 100%;
// $rightWidth: 40%;

.page-container {
  display: flex;
  background: #f5f5f5;
  overflow: hidden;

  .table-section {
    width: $leftWidth;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-header {
      margin-bottom: 15px;
      text-align: center;

      h2 {
        color: #333;
        font-size: 24px;
        font-weight: bold;
        margin: 0;
      }
    }

    .table-wrapper {
      flex: 1;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      overflow: hidden;
      position: relative;

      .loading-more-tip {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 1000;

        i {
          margin-right: 8px;
        }
      }

      .data-info {
        position: absolute;
        bottom: 5px;
        right: 10px;
        font-size: 12px;
        color: #666;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 4px;
        z-index: 999;

        .no-more-data {
          color: #999;
          margin-left: 8px;
        }
      }

      .scrolling-table {
        height: 100%;
        overflow: hidden;

        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }

        .price-table {
          width: 100%;
          height: 100%;

          // 表格头部样式
          /deep/ .el-table__header-wrapper {
            .el-table__header {
              th {
                background: #409eff;
                color: #ffffff;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid #ddd;

                .cell {
                  padding: 12px 8px;
                }
              }
            }
          }

          // 表格主体样式
          /deep/ .el-table__body-wrapper {
            // 隐藏滚动条
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            &::-webkit-scrollbar {
              display: none; /* Chrome, Safari, Opera */
            }

            .el-table__body {
              tr {
                td {
                  border-bottom: 1px solid #f0f0f0;
                  font-size: 13px;
                  color: #333;

                  .cell {
                    padding: 10px 8px;
                    text-align: center;
                  }

                  .price-text {
                    font-weight: bold;
                    color: #e74c3c;
                    font-size: 14px;
                  }
                }

                // 斑马纹样式
                &.el-table__row--striped {
                  td {
                    background-color: #fafafa;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // .video-section {
  //   width: $rightWidth;
  //   height: 100%;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   background: #000;

  //   .video-wrapper {
  //     width: 100%;
  //     height: 100%;
  //     position: relative;

  //     .price-video {
  //       width: 100%;
  //       height: 100%;
  //       object-fit: contain;
  //     }

  //     .video-error {
  //       position: absolute;
  //       top: 0;
  //       left: 0;
  //       width: 100%;
  //       height: 100%;
  //       display: flex;
  //       flex-direction: column;
  //       align-items: center;
  //       justify-content: center;
  //       background: rgba(0, 0, 0, 0.8);
  //       color: #ffffff;

  //       i {
  //         font-size: 48px;
  //         margin-bottom: 16px;
  //       }

  //       p {
  //         font-size: 16px;
  //         margin: 0;
  //       }
  //     }
  //   }
  // }
}

// Loading 样式覆盖
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);

  .el-loading-spinner {
    .el-loading-text {
      color: #409eff;
      font-weight: bold;
    }

    .circular {
      color: #409eff;
    }
  }
}
</style>
