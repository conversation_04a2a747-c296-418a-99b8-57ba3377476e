import { sendSoapRequest } from "./request.js";

/**
 * 物价查询API
 * 根据物价接口文档实现物价查询功能
 */

/**
 * 物价查询 (9100)
 * @param {Object} params - 查询参数
 * @param {string} params.alias - 收费项目别名 (空值表示查全部)
 * @param {string} params.tradeCode - 交易代码 (默认: 9100)
 * @param {number} params.page - 页码 (默认: 1)
 * @param {number} params.pageSize - 每页条数 (默认: 50)
 * @returns {Promise<Object>} 物价查询结果
 */
export async function queryPrice(params = {}) {
  try {
    const { alias = "", tradeCode = "9100", page = 1, pageSize = 50 } = params;

    // 构建请求消息XML，包含分页参数
    const requestMessage = `<Request><Alias>${alias}</Alias><TradeCode>${tradeCode}</TradeCode><Page>${page}</Page><PageSize>${pageSize}</PageSize></Request>`;

    console.log("物价查询请求参数:", params);

    // 发送SOAP请求
    const result = await sendSoapRequest("MES0061", requestMessage);

    if (result.success) {
      console.log("物价查询成功:", result.data);

      // 解析分页信息
      const responseData = result.data;
      const paginationInfo = {
        page: parseInt(responseData.Page) || page,
        pageSize: parseInt(responseData.PageSize) || pageSize,
        totalCount: parseInt(responseData.TotalCount) || 0,
        recordCount: parseInt(responseData.RecordCount) || 0,
        totalPages: parseInt(responseData.TotalPages) || 0,
      };

      return {
        success: true,
        data: responseData,
        pagination: paginationInfo,
        message: "物价查询成功",
      };
    } else {
      console.error("物价查询失败:", result.error);
      return {
        success: false,
        error: result.error,
        message: result.message || "物价查询失败",
      };
    }
  } catch (error) {
    console.error("物价查询异常:", error);
    return {
      success: false,
      error: "查询异常",
      message: error.message || "物价查询过程中发生异常",
    };
  }
}

// 导出所有API方法
export default {
  queryPrice,
};
