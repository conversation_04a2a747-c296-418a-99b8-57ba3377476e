东华医为对外标准接口文档
8383652


东华医为



1.接口标准
功能说明	根据交互操做服务编码和具体的消息流进行相应的交互操作
通讯方式	webServcie+Xml
服务地址	
方法名	HIPMessageServer(入参1 action,入参2 message)
	1、action类型为字符串，服务编号传给该参数，总线服务部署好提供
2、message类型为标准消息请求流传给该参数，为下列接口列表中的入参


1.1.对接方式
采用WebService方式。
1.2.消息格式
采用XML格式。
请求报文是以Request为根节点的XML串，应答报文是以Response为根节点的XML串。
2.接口地址
接口类型	接口地址
Webservice	https://**********:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS?WSDL=1

3.接口规范
3.1.获取排班及医生信息接口
3.1.1.接口流程说明
获取科室列表信息
获取医生列表信息
查询排班记录


3.1.2.二级科室列表(1012)
交易编码	1012
服务编码	MES0018
接口方法	QueryDepartment
描    述	查询科室列表
请求消息名	见“请求信息”表
应答消息名	见“返回信息”表
服务提供者	HIS
传输格式	XML

*******.请求信息
<Request>
	<TradeCode>1012</TradeCode>
	<ExtOrgCode></ExtOrgCode>
	<ClientType></ClientType>
	<HospitalId>SGSDYRMYY</HospitalId>
	<DepartmentType></DepartmentType>
	<DepartmentCode></DepartmentCode>
	<DepartmentGroupCode></DepartmentGroupCode>
	<ExtUserID>NOVA001</ExtUserID>
</Request>
字段	字段说明	类型	长度	空值	备注
TradeCode	交易代码	String	4	否	
ExtOrgCode	预约机构	String			
ClientType	客户端类型	String			
HospitalId	医院唯一编号	String		否	若存在多个院区必填
DepartmentType	科室类别	String			
DepartmentCode	科室代码	String			
ExtUserID	操作员代码	String		否	
DepartmentGroupCode	一级科室代码	String			如存在一二级科室并且需要可传

3.1.2.2.应答信息
<Response>
	<ResultCode>0</ResultCode>
	<RecordCount>116</RecordCount>
	<Departments>
		<Department>
			<DepartmentCode>140</DepartmentCode>
			<DepartmentName>预防保健科门诊</DepartmentName>
			<ParentId>-1</ParentId>
			<DepartmentHospCode>2</DepartmentHospCode>
			<DepartmentHospDesc>韶关市第一人民医院</DepartmentHospDesc>
		</Department>
		<Department>
			<DepartmentCode>141</DepartmentCode>
			<DepartmentName>营养科门诊</DepartmentName>
			<ParentId>-1</ParentId>
			<DepartmentHospCode>2</DepartmentHospCode>
			<DepartmentHospDesc>韶关市第一人民医院</DepartmentHospDesc>
		</Department>
	</Departments>
</Response>
字段	字段说明	类型	长度	空值	备注
ResultCode	交易结果	String		否	0：成功；
非0：错误
ResultContent	对错误/异常的详细描述信息	String		否	
Departments	集合，包含以下信息			否	
DepartmentCode	科室代码	String		否	
DepartmentName	科室名称	String		否	
RecordCount	记录数量	String			
ParentId	上级科室代码,没有则-1	String			
Description	科室简介	String			
DepartmentHospCode	院区编码	String			
DepartmentHospDesc	院区描述	String			
DepartmentAgeLimit	年龄限制	String			
Description	科室简介	String			
DepartmentAddress	科室地址	String		


3.1.3.医生列表(1013)
交易编码	1013
服务编号	MES0038
接口方法	QueryDoctor
描    述	查询医生列表
请求消息名	见“请求信息”表
应答消息名	见“返回信息”表
服务提供者	HIS
传输格式	XML

*******.请求信息
 <Request>
	<TradeCode>1013</TradeCode>
	<ExtOrgCode></ExtOrgCode>
	<ClientType></ClientType>
	<HospitalId>SGSDYRMYY</HospitalId>
	<ExtUserID>NOVA001</ExtUserID>
	<DepartmentCode>188</DepartmentCode>
  <DoctorCode></DoctorCode>
</Request> 
字段	字段说明	类型	长度	空值	备注
TradeCode	交易代码	String	4	否	
ExtOrgCode	预约机构	String			
ClientType	客户端类型	String			
HospitalId	医院唯一编号	String			若存在多个院区必填
ExtUserID	操作员代码	String		否	
DepartmentCode	科室代码	String		否	

*******.应答信息
<Response>
	<ResultCode>0</ResultCode>
	<RecordCount>32</RecordCount>
	<Doctors>
		<Doctor>
			<DoctorCode>128</DoctorCode>
			<DoctorName>李义强</DoctorName>
			<DoctotLevelCode>1</DoctotLevelCode>
			<DoctorLevel>主任医师</DoctorLevel>
			<DeptId>188</DeptId>
			<DeptName>骨科门诊</DeptName>
			<Description>现任广东省医师协会脊柱外科医师分会、广东省康复医学会脊柱脊髓分会、广东省健康中国研究会脊柱专业委员会、韶关市康复医学会神经康复分会委员；主持韶关市科学基金2项、发表论文10余篇，副主编《临床骨科常见疾病诊治策略》</Description>
			<DoctorStrength>脊柱退变性疾病的治疗(包括颈椎病，颈椎后纵韧带骨化症，腰椎间盘突出症，腰椎管狭窄症，腰椎滑脱症等)、骨质疏松患者的椎弓根螺钉固定及脊柱侧后凸畸形的矫形手术治疗。在脊柱微创内镜治疗方面有深入研究，曾在北京大学第三附属医院、广东省人民医院、广东省暨南大学附属医院、广东省中医院骨科精深学习脊柱微创技术；擅长各类脊柱微创手术如微创经皮椎弓根螺钉治疗胸腰椎骨折、经皮椎体成形、扩张通道下腰椎融合、椎间盘镜、椎间孔镜及单侧入路双通道内镜下髓核摘除、腰椎融合等。</DoctorStrength>
			<DoctorImg>https://**********:1443/imedical/web/html/doctorimg/100527.png</DoctorImg>
			<InsuScore>医保支付资格评分:总分12分,剩余12分 \n 医保支付状态:正常</InsuScore>
		</Doctor>
		<Doctor>
			<DoctorCode>129</DoctorCode>
			<DoctorName>钟小兵</DoctorName>
			<DoctotLevelCode>1</DoctotLevelCode>
			<DoctorLevel>主任医师</DoctorLevel>
			<DeptId>188</DeptId>
			<DeptName>骨科门诊</DeptName>
			<Description>关节镜下微创治疗膝关节及肩关节病、膝关节交叉韧带重建、膝关节半月板损伤修复等手术。</Description>
			<DoctorStrength>擅长颈椎病、腰椎间盘突出、腰椎管狭窄、腰椎滑脱、腰椎不稳、腰椎峡部裂、脊柱畸形、脊柱肿瘤、骨质疏松伴有病理性骨折、脊柱骨折创伤等多种疾病的开放手术治疗及微创手术治疗。</DoctorStrength>
			<DoctorImg>https://**********:1443/imedical/web/html/doctorimg/100658.png</DoctorImg>
			<InsuScore>医保支付资格评分:总分12分,剩余12分 \n 医保支付状态:正常</InsuScore>
		</Doctor>
	</Doctors>
</Response>
字段	字段说明	类型	长度	空值	备注
ResultCode	交易结果	String		否	0：成功；非0：错误
ResultContent	对错误/异常的详细描述信息	String		否	
RecordCount	记录数量	String		否	
Doctors	医生集合	String		否	
DoctorCode	医生代码	String		否	
DoctorName	医生名称	String		否	
DoctotLevelCode	医生职称代码	String		否	
DoctorLevel	医生职称	String		否	
DeptId	医生科室	String		否	
DeptName	医生科室名称	String			
Description	医生简介	String			
DoctorStrength	医生特长	String			
DoctorImg	医生头像	String			



3.1.4.查询排班记录(1004)
交易编码	1004
服务编号	MES0039
接口方法	QueryAdmSchedule
描    述	查询排班记录
请求消息名	见“请求信息”表
应答消息名	见“返回信息”表
服务提供者	HIS
传输格式	XML

*******.请求信息
<Request>
	<HospitalId>SGSDYRMYY</HospitalId>
	<ExtUserID>NOVA001</ExtUserID>
	<StartDate>2025-08-19</StartDate>
	<EndDate>2025-08-19</EndDate>
	<DepartmentCode>188</DepartmentCode>
	<DoctorCode>187</DoctorCode>
	<StopScheduleFlag>N</StopScheduleFlag>
	<RBASSessionCode>01</RBASSessionCode>
	<TradeCode>1004</TradeCode>
</Request>
字段	字段说明	类型	长度	空值	备注
HospitalId	医院唯一编号	String			若存在多个院区必填
TradeCode	交易代码	String	4	否	
ExtOrgCode	预约机构	String			
ClientType	客户端类型	String			
ExtUserID	操作员代码	String		否	
PatientID	登记号	String		否	医院会针对特定的患者采取优惠政策，第三方如果根据此接口查询出来的费用进行缴费，请务必录入PatientID
SessType	 号别				
StartDate	开始日期			否	(YYYY-MM-DD)
EndDate	结束日期			否	(YYYY-MM-DD)不允许查询多天的排版，请保持与开始日期一致
ServiceCode	专业代码				
DepartmentCode	科室代码			否	
DoctorCode	医生代码				
RBASSessionCode	出诊时段代码				
01：上午
02：下午
03：中午
04：晚上
05：全天
06：早晨
07：小夜诊
08：上午(韶关学院)
09：下午(韶关学院)
StopScheduleFlag	查询排班的标记				N:正常的排班    S:停诊的排班
CLGRPFlag	是否判断专病排班				需要按专病查询排班，传Y
CLGRPDesc	专病名称				按专病查询排班必填，2023.01.10添加
YMZDesc	新冠云门诊筛选				根据号别筛选，可直接传”新冠”

3.1.4.2.应答信息
<Response>
	<ResultCode>0</ResultCode>
	<RecordCount>2</RecordCount>
	<Schedules>
		<Schedule>
			<ScheduleItemCode>210||688</ScheduleItemCode>
			<ServiceDate>2025-08-19</ServiceDate>
			<WeekDay>2</WeekDay>
			<SessionCode>01</SessionCode>
			<SessionName>上午</SessionName>
			<StartTime>08:00</StartTime>
			<EndTime>12:00</EndTime>
			<DepartmentCode>188</DepartmentCode>
			<DepartmentName>骨科门诊</DepartmentName>
			<ClinicRoomCode/>
			<ClinicRoomName/>
			<DoctorCode>187</DoctorCode>
			<DoctorName>张浩波</DoctorName>
			<DoctorTitleCode>5</DoctorTitleCode>
			<DoctorTitle>副主任医师</DoctorTitle>
			<DoctorSpec/>
			<DoctorSessTypeCode>5</DoctorSessTypeCode>
			<DoctorSessType>副主任医师</DoctorSessType>
			<ServiceCode/>
			<ServiceName/>
			<Fee>19</Fee>
			<RegFee>0</RegFee>
			<CheckupFee>19</CheckupFee>
			<ServiceFee/>
			<OtherFee>0</OtherFee>
			<AvailableNumStr>36,37,38,39,40</AvailableNumStr>
			<AdmitAddress>2号楼二楼206诊室</AdmitAddress>
			<AdmitTimeRange/>
			<Note/>
			<ScheduleStatus>N</ScheduleStatus>
			<ScheduleStatusDesc>正常</ScheduleStatusDesc>
			<AvailableTotalNum>40</AvailableTotalNum>
			<AvailableLeftNum>5</AvailableLeftNum>
			<TimeRangeFlag>1</TimeRangeFlag>
			<ASNoLimitLoadFlag>N</ASNoLimitLoadFlag>
		</Schedule>
		<Schedule>
			<ScheduleItemCode>210||689</ScheduleItemCode>
			<ServiceDate>2025-08-19</ServiceDate>
			<WeekDay>2</WeekDay>
			<SessionCode>02</SessionCode>
			<SessionName>下午</SessionName>
			<StartTime>14:30</StartTime>
			<EndTime>17:30</EndTime>
			<DepartmentCode>188</DepartmentCode>
			<DepartmentName>骨科门诊</DepartmentName>
			<ClinicRoomCode/>
			<ClinicRoomName/>
			<DoctorCode>187</DoctorCode>
			<DoctorName>张浩波</DoctorName>
			<DoctorTitleCode>5</DoctorTitleCode>
			<DoctorTitle>副主任医师</DoctorTitle>
			<DoctorSpec/>
			<DoctorSessTypeCode>5</DoctorSessTypeCode>
			<DoctorSessType>副主任医师</DoctorSessType>
			<ServiceCode/>
			<ServiceName/>
			<Fee>19</Fee>
			<RegFee>0</RegFee>
			<CheckupFee>19</CheckupFee>
			<ServiceFee/>
			<OtherFee>0</OtherFee>
			<AvailableNumStr>2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30</AvailableNumStr>
			<AdmitAddress>2号楼二楼206诊室</AdmitAddress>
			<AdmitTimeRange/>
			<Note/>
			<ScheduleStatus>N</ScheduleStatus>
			<ScheduleStatusDesc>正常</ScheduleStatusDesc>
			<AvailableTotalNum>30</AvailableTotalNum>
			<AvailableLeftNum>29</AvailableLeftNum>
			<TimeRangeFlag>1</TimeRangeFlag>
			<ASNoLimitLoadFlag>N</ASNoLimitLoadFlag>
		</Schedule>
	</Schedules>
</Response>
字段	字段说明	类型	长度	空值	备注
ResultCode	交易结果	String		否	0：成功；非0：错误
ResultContent	对错误/异常的详细描述信息	String		否	
Schedules	数据集合			否	
ScheduleItemCode	门诊排班项记录标识	String		否	
ServiceDate	门诊排班日期	String		否	(YYYY-MM-DD)
WeekDay	星期数(1-7)	String			
SessionCode	排班时段代码	String			
SessionName	排班时段名称	String			
StartTime	开始时间	String			(HH:MM)
EndTime	结束时间	String			(HH:MM)
DepartmentCode	科室代码	String			
DepartmentName	科室名称	String			
ClinicRoomCode	诊室代码	String			
ClinicRoomName	诊室名称	String			
DoctorCode	医生代码	String			
DoctorName	医生名称	String			
DoctorTitleCode	医生职称代码	String			
DoctorTitle	医生职称	String			
DoctorSpec	医生专长	String			
DoctorSessTypeCode	出诊级别代码	String			
DoctorSessType	出诊级别	String			
ServiceCode	专业代码	String			
ServiceName	专业名称	String			
Fee	预约挂号总费用	String			单位：元
RegFee	挂号费	String			单位：元
CheckupFee	诊查费	String			单位：元
ServiceFee	服务费	String			单位：元
OtherFee	其它费	String			单位：元
AvailableNumStr	剩余号信息串	String			
AdmitAddress	就诊地址	String			
AdmitTimeRange	候诊时间范围	String			
Note	备注	String			
RecordCount	记录数量	String			
StartTime	班别开始时间	String			格式：HH:MI
EndTime	班别结束时间	String			格式：HH:MI
TimeRangeFlag	是否有分时	String			0-否  1-是
ScheduleStatus	就诊状态	String			
AvailableTotalNum	该时段可预约的总号源数	String			
AvailableLeftNum	该时段剩余号源数	String	


3.1.5.医生号源分时信息查询(10041)
交易编码	10041
服务编号	MES0040
接口方法	QueryScheduleTimeInfo
描    述	查询医生号源分时信息查询（仅限有分时段的排班）
请求消息名	见“请求信息”表
应答消息名	见“返回信息”表
服务提供者	HIS
传输格式	XML

*******.请求信息
<Request>
	<TradeCode>10041</TradeCode>
	<HospitalId>SGSDYRMYY</HospitalId>
	<ExtUserID>NOVA001</ExtUserID>
	<DepartmentCode>146</DepartmentCode>
	<DoctorCode>1210</DoctorCode>
	<RBASSessionCode>05</RBASSessionCode>
	<ScheduleItemCode></ScheduleItemCode>
	<ServiceDate>2024-02-23</ServiceDate>
</Request>
字段	字段说明	类型	长度	空值	备注
TradeCode	交易代码	String	4	否	
ExtOrgCode	预约机构	String			
ClientType	客户端类型	String			
HospitalId	医院唯一编号	String			若存在多个院区必填
ExtUserID	操作员代码	String		否	
ScheduleItemCode	号别代码	String		否	
DepartmentCode	科室代码			否	ScheduleItemCode为空时此项必填
DoctorCode	医生代码			否	ScheduleItemCode为空时此项必填
RBASSessionCode	出诊时段代码				
01：上午
02：下午
03：中午
04：晚上
05：全天
06：早晨
07：小夜诊
08：上午(韶关学院)
09：下午(韶关学院)
ServiceDate	出诊日期			否	ScheduleItemCode为空时此项必填

3.1.5.2.应答信息
<Response>
	<ResultCode>0</ResultCode>
	<RecordCount>48</RecordCount>
	<TimeRanges>
		<TimeRange>
			<ScheduleItemCode>1637||89</ScheduleItemCode>
			<ServiceDate>2024-02-23</ServiceDate>
			<WeekDay>5</WeekDay>
			<SessionCode>05</SessionCode>
			<SessionName>全天</SessionName>
			<StartTime>00:00:00</StartTime>
			<EndTime>00:30:00</EndTime>
			<AvailableNumStr>1,2,3,4,5,6,7,8,9,10</AvailableNumStr>
			<AvailableTotalNum>10</AvailableTotalNum>
			<AvailableLeftNum>10</AvailableLeftNum>
		</TimeRange>
		<TimeRange>
			<ScheduleItemCode>1637||89</ScheduleItemCode>
			<ServiceDate>2024-02-23</ServiceDate>
			<WeekDay>5</WeekDay>
			<SessionCode>05</SessionCode>
			<SessionName>全天</SessionName>
			<StartTime>00:30:00</StartTime>
			<EndTime>01:00:00</EndTime>
			<AvailableNumStr>11,12,13,14,15,16,17,18,19,20</AvailableNumStr>
			<AvailableTotalNum>10</AvailableTotalNum>
			<AvailableLeftNum>10</AvailableLeftNum>
		</TimeRange>
	</TimeRanges>
</Response>
字段	字段说明	类型	长度	空值	备注
ResultCode	交易结果：	String		否	0：成功；非0：错误
ResultContent	对错误/异常的详细描述信息	String		否	
TimeRanges	数据集合			否	
ScheduleItemCode	门诊排班项记录标识	String		否	
ServiceDate	门诊排班日期	String		否	(YYYY-MM-DD)
WeekDay	星期数(1-7)	String		否	
SessionCode	排班时段代码	String		否	
SessionName	排班时段名称	String		否	
AvailableNumStr	剩余号信息串	String		否	
TimeRangeSeqNo	分时号	String		否	
RecordCount	记录数量	String		否	
StartTime	班别开始时间	String		否	格式：HH:MI
EndTime	班别结束时间	String		否	格式：HH:MI
AvailableTotalNum	该时段可预约的总号源数	String		否	
AvailableLeftNum	该时段剩余号源数	String		否	
