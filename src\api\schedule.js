import { sendSoapRequest } from './request.js'
import { API_ACTIONS } from './config.js'

/**
 * 排班相关API
 * 根据接口文档实现排班查询功能
 */

/**
 * 查询排班记录 (1004)
 * @param {Object} params - 查询参数
 * @param {string} params.hospitalId - 医院唯一编号 (默认: SGSDYRMYY)
 * @param {string} params.extUserId - 操作员代码 (默认: NOVA001)
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD) (必填)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD) (必填)
 * @param {string} params.departmentCode - 科室代码 (必填)
 * @param {string} params.doctorCode - 医生代码 (可选)
 * @param {string} params.stopScheduleFlag - 查询排班标记 (默认: N)
 * @param {string} params.rbasSessionCode - 出诊时段代码 (可选)
 * @returns {Promise<Object>} 排班查询结果
 */
export async function querySchedule(params = {}) {
  try {
    const {
      hospitalId = 'SGSDYRMYY',
      extUserId = 'NOVA001',
      startDate,
      endDate,
      departmentCode,
      doctorCode = '',
      stopScheduleFlag = 'N',
      rbasSessionCode = ''
    } = params

    // 必填参数验证
    if (!startDate || !endDate || !departmentCode) {
      return {
        success: false,
        error: '参数错误',
        message: '开始日期、结束日期和科室代码不能为空'
      }
    }

    // 构建请求消息XML
    const requestMessage = `<Request>
  <HospitalId>${hospitalId}</HospitalId>
  <ExtUserID>${extUserId}</ExtUserID>
  <StartDate>${startDate}</StartDate>
  <EndDate>${endDate}</EndDate>
  <DepartmentCode>${departmentCode}</DepartmentCode>
  <DoctorCode>${doctorCode}</DoctorCode>
  <StopScheduleFlag>${stopScheduleFlag}</StopScheduleFlag>
  <RBASSessionCode>${rbasSessionCode}</RBASSessionCode>
  <TradeCode>1004</TradeCode>
</Request>`

    console.log('排班查询请求参数:', params)

    // 发送SOAP请求
    const result = await sendSoapRequest(API_ACTIONS.QUERY_SCHEDULE, requestMessage)

    if (result.success) {
      console.log('排班查询成功:', result.data)
      return {
        success: true,
        data: result.data,
        message: '排班查询成功'
      }
    } else {
      console.error('排班查询失败:', result.error)
      return {
        success: false,
        error: result.error,
        message: result.message || '排班查询失败'
      }
    }
  } catch (error) {
    console.error('排班查询异常:', error)
    return {
      success: false,
      error: '查询异常',
      message: error.message || '排班查询过程中发生异常'
    }
  }
}

/**
 * 查询医生号源分时信息 (10041)
 * @param {Object} params - 查询参数
 * @param {string} params.hospitalId - 医院唯一编号 (默认: SGSDYRMYY)
 * @param {string} params.extUserId - 操作员代码 (默认: NOVA001)
 * @param {string} params.departmentCode - 科室代码 (必填)
 * @param {string} params.doctorCode - 医生代码 (必填)
 * @param {string} params.serviceDate - 出诊日期 (YYYY-MM-DD) (必填)
 * @param {string} params.rbasSessionCode - 出诊时段代码 (可选)
 * @param {string} params.scheduleItemCode - 号别代码 (可选)
 * @returns {Promise<Object>} 分时信息查询结果
 */
export async function queryScheduleTimeInfo(params = {}) {
  try {
    const {
      hospitalId = 'SGSDYRMYY',
      extUserId = 'NOVA001',
      departmentCode,
      doctorCode,
      serviceDate,
      rbasSessionCode = '',
      scheduleItemCode = ''
    } = params

    // 必填参数验证
    if (!departmentCode || !doctorCode || !serviceDate) {
      return {
        success: false,
        error: '参数错误',
        message: '科室代码、医生代码和出诊日期不能为空'
      }
    }

    // 构建请求消息XML
    const requestMessage = `<Request>
  <TradeCode>10041</TradeCode>
  <HospitalId>${hospitalId}</HospitalId>
  <ExtUserID>${extUserId}</ExtUserID>
  <DepartmentCode>${departmentCode}</DepartmentCode>
  <DoctorCode>${doctorCode}</DoctorCode>
  <RBASSessionCode>${rbasSessionCode}</RBASSessionCode>
  <ScheduleItemCode>${scheduleItemCode}</ScheduleItemCode>
  <ServiceDate>${serviceDate}</ServiceDate>
</Request>`

    console.log('分时信息查询请求参数:', params)

    // 发送SOAP请求
    const result = await sendSoapRequest(API_ACTIONS.QUERY_TIME_INFO, requestMessage)

    if (result.success) {
      console.log('分时信息查询成功:', result.data)
      return {
        success: true,
        data: result.data,
        message: '分时信息查询成功'
      }
    } else {
      console.error('分时信息查询失败:', result.error)
      return {
        success: false,
        error: result.error,
        message: result.message || '分时信息查询失败'
      }
    }
  } catch (error) {
    console.error('分时信息查询异常:', error)
    return {
      success: false,
      error: '查询异常',
      message: error.message || '分时信息查询过程中发生异常'
    }
  }
}

/**
 * 查询今日排班
 * @param {string} departmentCode - 科室代码
 * @param {string} doctorCode - 医生代码 (可选)
 * @returns {Promise<Object>} 今日排班查询结果
 */
export async function queryTodaySchedule(departmentCode, doctorCode = '') {
  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD格式
  return await querySchedule({
    startDate: today,
    endDate: today,
    departmentCode,
    doctorCode
  })
}

/**
 * 查询指定日期的排班
 * @param {string} date - 日期 (YYYY-MM-DD)
 * @param {string} departmentCode - 科室代码
 * @param {string} doctorCode - 医生代码 (可选)
 * @returns {Promise<Object>} 排班查询结果
 */
export async function queryScheduleByDate(date, departmentCode, doctorCode = '') {
  return await querySchedule({
    startDate: date,
    endDate: date,
    departmentCode,
    doctorCode
  })
}

// 导出所有API方法
export default {
  querySchedule,
  queryScheduleTimeInfo,
  queryTodaySchedule,
  queryScheduleByDate
}
