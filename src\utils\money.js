/**
 * 保留小数
 * @param {Number|String} str - 需要处理的值
 * @param {Number} [precision=2] - 保留几位小数
 * @returns {string}
 */
export const toFixed = (str, precision = 2) => {
  if (!str && str !== 0) return "";
  if (typeof str === "number") str = str.toString();

  let [decimal0, decimal1] = str.split(".");

  if (!decimal1) {
    str = `${decimal0}.${"".padEnd(precision, "0")}`;
  } else {
    decimal1 = decimal1.slice(0, precision);
    str = `${decimal0}.${decimal1.padEnd(precision, "0")}`;
  }
  return str;
};